#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <regex>
#include <memory>
#include <algorithm>
#include <sstream>
#include <queue>
#include <stack>
#include <functional>
// Optional JSON support - comment out if not available
// #include <json/json.h>
#define JSON_SUPPORT_DISABLED
#include <iomanip>
#include <chrono>

// Vulnerability types enumeration
enum class VulnType {
    AUTH_BYPASS,
    AUTHORIZATION_FLAW,
    BUSINESS_RULE_VIOLATION,
    DATA_FLOW_ANOMALY,
    TOCTOU_RACE_CONDITION,
    HARDCODED_CREDENTIAL,
    MISSING_VALIDATION,
    BUFFER_OVERFLOW,
    SQL_INJECTION,
    XSS,
    COMMAND_INJECTION,
    PATH_TRAVERSAL,
    INSECURE_CRYPTO,
    WEAK_RANDOM,
    INFORMATION_DISCLOSURE
};

// Severity levels enumeration
enum class Severity {
    LOW = 1,
    MEDIUM = 2,
    HIGH = 3,
    CRITICAL = 4
};

// Enhanced AST Node with better type system
class EnhancedASTNode {
public:
    enum NodeType {
        FUNCTION_DEF, IF_STATEMENT, WHILE_LOOP, FOR_LOOP,
        ASSIGNMENT, FUNCTION_CALL, VARIABLE_DECL, RETURN_STMT,
        BLOCK, EXPRESSION, BINARY_OP, UNARY_OP, LITERAL,
        POINTER_DEREF, ARRAY_ACCESS, STRUCT_ACCESS
    };

    NodeType type;
    std::string value;
    std::string data_type;
    std::vector<std::shared_ptr<EnhancedASTNode>> children;
    std::unordered_map<std::string, std::string> attributes;
    int line_number;
    int column_number;
    std::string file_path;
    
    // Control flow information
    std::vector<std::shared_ptr<EnhancedASTNode>> predecessors;
    std::vector<std::shared_ptr<EnhancedASTNode>> successors;
    
    EnhancedASTNode(NodeType t, const std::string& v, int line = 0, int col = 0) 
        : type(t), value(v), line_number(line), column_number(col) {}
    
    void add_child(std::shared_ptr<EnhancedASTNode> child) {
        children.push_back(child);
    }
    
    void set_attribute(const std::string& key, const std::string& value) {
        attributes[key] = value;
    }
    
    std::string get_attribute(const std::string& key) const {
        auto it = attributes.find(key);
        return it != attributes.end() ? it->second : "";
    }
};

// Symbol table for tracking variable scope and types
class SymbolTable {
private:
    std::vector<std::unordered_map<std::string, std::string>> scopes;
    
public:
    void push_scope() {
        scopes.emplace_back();
    }
    
    void pop_scope() {
        if (!scopes.empty()) {
            scopes.pop_back();
        }
    }
    
    void define_symbol(const std::string& name, const std::string& type) {
        if (!scopes.empty()) {
            scopes.back()[name] = type;
        }
    }
    
    std::string lookup_symbol(const std::string& name) const {
        for (auto it = scopes.rbegin(); it != scopes.rend(); ++it) {
            auto found = it->find(name);
            if (found != it->end()) {
                return found->second;
            }
        }
        return "";
    }
    
    bool is_defined(const std::string& name) const {
        return !lookup_symbol(name).empty();
    }
};

// Control Flow Graph for advanced analysis
class ControlFlowGraph {
private:
    std::vector<std::shared_ptr<EnhancedASTNode>> nodes;
    std::unordered_map<std::shared_ptr<EnhancedASTNode>, std::vector<std::shared_ptr<EnhancedASTNode>>> edges;
    
public:
    void add_node(std::shared_ptr<EnhancedASTNode> node) {
        nodes.push_back(node);
    }
    
    void add_edge(std::shared_ptr<EnhancedASTNode> from, std::shared_ptr<EnhancedASTNode> to) {
        edges[from].push_back(to);
        to->predecessors.push_back(from);
        from->successors.push_back(to);
    }
    
    std::vector<std::vector<std::shared_ptr<EnhancedASTNode>>> find_all_paths(
        std::shared_ptr<EnhancedASTNode> start,
        std::shared_ptr<EnhancedASTNode> end) {
        
        std::vector<std::vector<std::shared_ptr<EnhancedASTNode>>> all_paths;
        std::vector<std::shared_ptr<EnhancedASTNode>> current_path;
        std::unordered_set<std::shared_ptr<EnhancedASTNode>> visited;
        
        dfs_paths(start, end, current_path, visited, all_paths);
        return all_paths;
    }
    
private:
    void dfs_paths(std::shared_ptr<EnhancedASTNode> current,
                   std::shared_ptr<EnhancedASTNode> target,
                   std::vector<std::shared_ptr<EnhancedASTNode>>& path,
                   std::unordered_set<std::shared_ptr<EnhancedASTNode>>& visited,
                   std::vector<std::vector<std::shared_ptr<EnhancedASTNode>>>& all_paths) {
        
        if (visited.find(current) != visited.end()) return;
        
        visited.insert(current);
        path.push_back(current);
        
        if (current == target) {
            all_paths.push_back(path);
        } else {
            auto it = edges.find(current);
            if (it != edges.end()) {
                for (const auto& neighbor : it->second) {
                    dfs_paths(neighbor, target, path, visited, all_paths);
                }
            }
        }
        
        path.pop_back();
        visited.erase(current);
    }
};

// Enhanced vulnerability finding with more context
struct EnhancedFinding {
    VulnType type;
    Severity severity;
    std::string title;
    std::string description;
    std::string recommendation;
    std::string file_path;
    int line_number;
    int column_number;
    std::string function_name;
    std::string code_snippet;
    std::vector<std::string> evidence;
    std::vector<std::string> execution_paths;
    double confidence_score;
    std::unordered_map<std::string, std::string> metadata;
    
    EnhancedFinding(VulnType t, Severity s, const std::string& title, 
                   const std::string& desc, const std::string& file, 
                   int line, int col, const std::string& func)
        : type(t), severity(s), title(title), description(desc), 
          file_path(file), line_number(line), column_number(col), 
          function_name(func), confidence_score(0.8) {}
};

// Advanced pattern matching system
class PatternMatcher {
private:
    std::vector<std::regex> compiled_patterns;
    std::vector<std::function<bool(const std::string&)>> custom_validators;
    
public:
    void add_regex_pattern(const std::string& pattern) {
        compiled_patterns.emplace_back(pattern, std::regex_constants::icase);
    }
    
    void add_custom_validator(std::function<bool(const std::string&)> validator) {
        custom_validators.push_back(validator);
    }
    
    bool matches(const std::string& text) const {
        // Check regex patterns
        for (const auto& pattern : compiled_patterns) {
            if (std::regex_search(text, pattern)) {
                return true;
            }
        }
        
        // Check custom validators
        for (const auto& validator : custom_validators) {
            if (validator(text)) {
                return true;
            }
        }
        
        return false;
    }
    
    std::vector<std::string> extract_matches(const std::string& text) const {
        std::vector<std::string> matches;
        std::smatch match;
        
        for (const auto& pattern : compiled_patterns) {
            if (std::regex_search(text, match, pattern)) {
                matches.push_back(match.str());
            }
        }
        
        return matches;
    }
};

// Advanced taint analysis with inter-procedural support
class AdvancedTaintAnalyzer {
private:
    std::unordered_map<std::string, std::unordered_set<std::string>> function_taints;
    std::unordered_set<std::string> current_tainted_vars;
    SymbolTable* symbol_table;
    
    // Predefined source and sink functions
    std::unordered_map<std::string, std::vector<int>> taint_sources = {
        {"scanf", {1}}, {"gets", {0}}, {"fgets", {0}}, {"getenv", {-1}},
        {"recv", {1}}, {"read", {1}}, {"getchar", {-1}}, {"cin", {-1}}
    };
    
    std::unordered_map<std::string, std::vector<int>> taint_sinks = {
        {"system", {0}}, {"exec", {0, 1}}, {"popen", {0}}, {"printf", {0}},
        {"sprintf", {1}}, {"strcpy", {0}}, {"strcat", {0}}, {"memcpy", {0}}
    };
    
public:
    AdvancedTaintAnalyzer(SymbolTable* st) : symbol_table(st) {}
    
    std::vector<EnhancedFinding> analyze_function(
        const std::shared_ptr<EnhancedASTNode>& func_node,
        const std::string& file_path) {
        
        std::vector<EnhancedFinding> findings;
        current_tainted_vars.clear();
        
        analyze_node_taint(func_node, findings, file_path);
        
        return findings;
    }
    
private:
    void analyze_node_taint(const std::shared_ptr<EnhancedASTNode>& node,
                           std::vector<EnhancedFinding>& findings,
                           const std::string& file_path) {
        if (!node) return;
        
        switch (node->type) {
            case EnhancedASTNode::FUNCTION_CALL:
                analyze_function_call_taint(node, findings, file_path);
                break;
            case EnhancedASTNode::ASSIGNMENT:
                analyze_assignment_taint(node);
                break;
            default:
                break;
        }
        
        for (const auto& child : node->children) {
            analyze_node_taint(child, findings, file_path);
        }
    }
    
    void analyze_function_call_taint(const std::shared_ptr<EnhancedASTNode>& node,
                                    std::vector<EnhancedFinding>& findings,
                                    const std::string& file_path) {
        std::string func_name = extract_function_name(node->value);
        
        // Check if it's a taint source
        auto source_it = taint_sources.find(func_name);
        if (source_it != taint_sources.end()) {
            std::vector<std::string> args = extract_function_args(node->value);
            for (int arg_idx : source_it->second) {
                if (arg_idx == -1) {
                    // Return value is tainted
                    std::string result_var = extract_assignment_target(node->value);
                    if (!result_var.empty()) {
                        current_tainted_vars.insert(result_var);
                    }
                } else if (arg_idx < args.size()) {
                    current_tainted_vars.insert(args[arg_idx]);
                }
            }
        }
        
        // Check if it's a taint sink
        auto sink_it = taint_sinks.find(func_name);
        if (sink_it != taint_sinks.end()) {
            std::vector<std::string> args = extract_function_args(node->value);
            for (int arg_idx : sink_it->second) {
                if (arg_idx < args.size()) {
                    std::string arg = args[arg_idx];
                    if (is_tainted(arg)) {
                        EnhancedFinding finding(
                            VulnType::DATA_FLOW_ANOMALY,
                            Severity::HIGH,
                            "Tainted Data Reaches Sensitive Function",
                            "User-controlled data flows to a sensitive function without proper validation",
                            file_path, node->line_number, node->column_number,
                            node->get_attribute("function_context")
                        );
                        finding.code_snippet = node->value;
                        finding.evidence.push_back("Tainted argument: " + arg);
                        finding.evidence.push_back("Sink function: " + func_name);
                        finding.confidence_score = calculate_taint_confidence(arg, func_name);
                        findings.push_back(finding);
                    }
                }
            }
        }
    }
    
    void analyze_assignment_taint(const std::shared_ptr<EnhancedASTNode>& node) {
        std::regex assign_regex(R"((\w+)\s*=\s*(.+))");
        std::smatch match;
        
        if (std::regex_search(node->value, match, assign_regex)) {
            std::string lhs = match[1].str();
            std::string rhs = match[2].str();
            
            // Check if RHS contains tainted variables
            bool rhs_tainted = false;
            for (const auto& tainted_var : current_tainted_vars) {
                if (rhs.find(tainted_var) != std::string::npos) {
                    rhs_tainted = true;
                    break;
                }
            }
            
            if (rhs_tainted) {
                current_tainted_vars.insert(lhs);
            } else {
                current_tainted_vars.erase(lhs);
            }
        }
    }
    
    bool is_tainted(const std::string& var) const {
        return current_tainted_vars.find(var) != current_tainted_vars.end();
    }
    
    double calculate_taint_confidence(const std::string& var, const std::string& sink) const {
        double base_confidence = 0.7;
        
        // Increase confidence for high-risk sinks
        if (sink == "system" || sink == "exec" || sink == "popen") {
            base_confidence += 0.2;
        }
        
        // Decrease confidence if variable name suggests it might be sanitized
        if (var.find("clean") != std::string::npos || 
            var.find("safe") != std::string::npos ||
            var.find("sanitized") != std::string::npos) {
            base_confidence -= 0.3;
        }
        
        return std::min(1.0, std::max(0.1, base_confidence));
    }
    
    std::string extract_function_name(const std::string& call) const {
        std::regex func_regex(R"((\w+)\s*\()");
        std::smatch match;
        if (std::regex_search(call, match, func_regex)) {
            return match[1].str();
        }
        return "";
    }
    
    std::vector<std::string> extract_function_args(const std::string& call) const {
        std::vector<std::string> args;
        std::regex args_regex(R"(\((.*)\))");
        std::smatch match;
        
        if (std::regex_search(call, match, args_regex)) {
            std::string args_str = match[1].str();
            std::stringstream ss(args_str);
            std::string arg;
            
            while (std::getline(ss, arg, ',')) {
                arg.erase(0, arg.find_first_not_of(" \t"));
                arg.erase(arg.find_last_not_of(" \t") + 1);
                if (!arg.empty()) {
                    args.push_back(arg);
                }
            }
        }
        
        return args;
    }
    
    std::string extract_assignment_target(const std::string& stmt) const {
        std::regex assign_regex(R"((\w+)\s*=)");
        std::smatch match;
        if (std::regex_search(stmt, match, assign_regex)) {
            return match[1].str();
        }
        return "";
    }
};

// Business logic rule engine with configurable rules
class BusinessLogicRuleEngine {
private:
    struct BusinessRule {
        std::string name;
        std::string description;
        VulnType vuln_type;
        Severity severity;
        PatternMatcher pattern_matcher;
        std::function<bool(const std::shared_ptr<EnhancedASTNode>&)> validator;
        std::vector<std::string> required_context;
        double base_confidence;
    };
    
    std::vector<BusinessRule> rules;
    
public:
    void load_rules_from_config(const std::string& config_file) {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Warning: Could not open config file " << config_file << std::endl;
            load_default_rules();
            return;
        }

#ifdef JSON_SUPPORT_DISABLED
        std::cerr << "Warning: JSON support disabled. Using default rules instead." << std::endl;
        load_default_rules();
        file.close();
        return;
#else
        Json::Value root;
        file >> root;

        for (const auto& rule_json : root["rules"]) {
            BusinessRule rule;
            rule.name = rule_json["name"].asString();
            rule.description = rule_json["description"].asString();
            rule.vuln_type = string_to_vuln_type(rule_json["vuln_type"].asString());
            rule.severity = string_to_severity(rule_json["severity"].asString());
            rule.base_confidence = rule_json.get("confidence", 0.8).asDouble();

            // Load patterns
            for (const auto& pattern : rule_json["patterns"]) {
                rule.pattern_matcher.add_regex_pattern(pattern.asString());
            }

            // Load required context
            for (const auto& context : rule_json["required_context"]) {
                rule.required_context.push_back(context.asString());
            }

            rules.push_back(rule);
        }
#endif
    }
    
    void load_default_rules() {
        // Authentication bypass rules
        BusinessRule auth_bypass_rule;
        auth_bypass_rule.name = "hardcoded_auth_bypass";
        auth_bypass_rule.description = "Hardcoded authentication bypass detected";
        auth_bypass_rule.vuln_type = VulnType::AUTH_BYPASS;
        auth_bypass_rule.severity = Severity::CRITICAL;
        auth_bypass_rule.base_confidence = 0.9;
        
        auth_bypass_rule.pattern_matcher.add_regex_pattern(R"(==\s*"admin")");
        auth_bypass_rule.pattern_matcher.add_regex_pattern(R"(==\s*"password")");
        auth_bypass_rule.pattern_matcher.add_regex_pattern(R"(==\s*1234)");
        auth_bypass_rule.pattern_matcher.add_regex_pattern(R"(==\s*0x[A-Fa-f0-9]+)");
        
        auth_bypass_rule.validator = [](const std::shared_ptr<EnhancedASTNode>& node) {
            return node->type == EnhancedASTNode::IF_STATEMENT;
        };
        
        rules.push_back(auth_bypass_rule);
        
        // Business rule validation
        BusinessRule biz_validation_rule;
        biz_validation_rule.name = "missing_business_validation";
        biz_validation_rule.description = "Missing validation in business logic";
        biz_validation_rule.vuln_type = VulnType::BUSINESS_RULE_VIOLATION;
        biz_validation_rule.severity = Severity::HIGH;
        biz_validation_rule.base_confidence = 0.7;
        
        biz_validation_rule.pattern_matcher.add_regex_pattern(R"(\b(amount|price|balance|payment|charge)\b)");
        biz_validation_rule.required_context = {"> 0", ">= 0", "< 0", "validate", "check"};
        
        rules.push_back(biz_validation_rule);
        
        // Buffer overflow prevention
        BusinessRule buffer_rule;
        buffer_rule.name = "unsafe_buffer_operations";
        buffer_rule.description = "Unsafe buffer operations detected";
        buffer_rule.vuln_type = VulnType::MISSING_VALIDATION;
        buffer_rule.severity = Severity::HIGH;
        buffer_rule.base_confidence = 0.8;
        
        buffer_rule.pattern_matcher.add_regex_pattern(R"(\b(strcpy|strcat|sprintf|gets)\b)");
        buffer_rule.required_context = {"strlen", "sizeof", "bounds", "length"};
        
        rules.push_back(buffer_rule);
    }
    
    std::vector<EnhancedFinding> apply_rules(const std::shared_ptr<EnhancedASTNode>& ast,
                                           const std::string& file_path) {
        std::vector<EnhancedFinding> findings;
        
        for (const auto& rule : rules) {
            auto rule_findings = apply_single_rule(rule, ast, file_path);
            findings.insert(findings.end(), rule_findings.begin(), rule_findings.end());
        }
        
        return findings;
    }
    
private:
    std::vector<EnhancedFinding> apply_single_rule(const BusinessRule& rule,
                                                  const std::shared_ptr<EnhancedASTNode>& node,
                                                  const std::string& file_path) {
        std::vector<EnhancedFinding> findings;
        apply_rule_to_node(rule, node, findings, file_path);
        return findings;
    }
    
    void apply_rule_to_node(const BusinessRule& rule,
                           const std::shared_ptr<EnhancedASTNode>& node,
                           std::vector<EnhancedFinding>& findings,
                           const std::string& file_path) {
        if (!node) return;
        
        // Check if pattern matches
        if (rule.pattern_matcher.matches(node->value)) {
            // Apply custom validator if present
            if (!rule.validator || rule.validator(node)) {
                // Check required context
                bool has_required_context = rule.required_context.empty() || 
                                          check_required_context(rule, node);
                
                if (!has_required_context) {
                    EnhancedFinding finding(
                        rule.vuln_type, rule.severity, rule.name, rule.description,
                        file_path, node->line_number, node->column_number,
                        node->get_attribute("function_context")
                    );
                    
                    finding.code_snippet = node->value;
                    finding.confidence_score = rule.base_confidence;
                    
                    auto matches = rule.pattern_matcher.extract_matches(node->value);
                    for (const auto& match : matches) {
                        finding.evidence.push_back("Pattern matched: " + match);
                    }
                    
                    if (!rule.required_context.empty()) {
                        finding.evidence.push_back("Missing required validation context");
                        finding.recommendation = "Add proper validation checks before this operation";
                    }
                    
                    findings.push_back(finding);
                }
            }
        }
        
        // Recursively check children
        for (const auto& child : node->children) {
            apply_rule_to_node(rule, child, findings, file_path);
        }
    }
    
    bool check_required_context(const BusinessRule& rule, 
                               const std::shared_ptr<EnhancedASTNode>& node) {
        // Check in current node and nearby nodes for required context
        std::string context = node->value;
        
        // Check parent and sibling nodes for context
        // This is a simplified implementation
        for (const auto& required : rule.required_context) {
            if (context.find(required) != std::string::npos) {
                return true;
            }
        }
        
        return false;
    }
    
    VulnType string_to_vuln_type(const std::string& str) {
        if (str == "AUTH_BYPASS") return VulnType::AUTH_BYPASS;
        if (str == "AUTHORIZATION_FLAW") return VulnType::AUTHORIZATION_FLAW;
        if (str == "BUSINESS_RULE_VIOLATION") return VulnType::BUSINESS_RULE_VIOLATION;
        if (str == "DATA_FLOW_ANOMALY") return VulnType::DATA_FLOW_ANOMALY;
        if (str == "TOCTOU_RACE_CONDITION") return VulnType::TOCTOU_RACE_CONDITION;
        if (str == "HARDCODED_CREDENTIAL") return VulnType::HARDCODED_CREDENTIAL;
        if (str == "MISSING_VALIDATION") return VulnType::MISSING_VALIDATION;
        return VulnType::MISSING_VALIDATION;
    }
    
    Severity string_to_severity(const std::string& str) {
        if (str == "LOW") return Severity::LOW;
        if (str == "MEDIUM") return Severity::MEDIUM;
        if (str == "HIGH") return Severity::HIGH;
        if (str == "CRITICAL") return Severity::CRITICAL;
        return Severity::MEDIUM;
    }
};

// Enhanced main scanner with all components integrated
class AdvancedBusinessLogicScanner {
private:
    BusinessLogicRuleEngine rule_engine;
    SymbolTable symbol_table;
    ControlFlowGraph cfg;
    
public:
    AdvancedBusinessLogicScanner(const std::string& config_file = "") {
        if (!config_file.empty()) {
            rule_engine.load_rules_from_config(config_file);
        } else {
            rule_engine.load_default_rules();
        }
    }
    
    std::vector<EnhancedFinding> scan_file(const std::string& file_path) {
        std::vector<EnhancedFinding> findings;
        
        // Read file
        std::ifstream file(file_path);
        if (!file.is_open()) {
            std::cerr << "Error: Cannot open file " << file_path << std::endl;
            return findings;
        }
        
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();
        
        // Parse (simplified for demo)
        auto ast = parse_content(content, file_path);
        
        // Build symbol table and CFG
        build_symbol_table(ast);
        build_control_flow_graph(ast);
        
        // Apply business logic rules
        auto rule_findings = rule_engine.apply_rules(ast, file_path);
        findings.insert(findings.end(), rule_findings.begin(), rule_findings.end());
        
        // Run taint analysis
        AdvancedTaintAnalyzer taint_analyzer(&symbol_table);
        auto taint_findings = run_taint_analysis(ast, file_path, taint_analyzer);
        findings.insert(findings.end(), taint_findings.begin(), taint_findings.end());
        
        // Sort findings by severity and confidence
        std::sort(findings.begin(), findings.end(), 
                 [](const EnhancedFinding& a, const EnhancedFinding& b) {
                     if (a.severity != b.severity) {
                         return static_cast<int>(a.severity) > static_cast<int>(b.severity);
                     }
                     return a.confidence_score > b.confidence_score;
                 });
        
        return findings;
    }
    
private:
    std::shared_ptr<EnhancedASTNode> parse_content(const std::string& content, 
                                                  const std::string& file_path) {
        // Simplified parser - in real implementation, use Clang LibTooling
        auto root = std::make_shared<EnhancedASTNode>(EnhancedASTNode::BLOCK, "root");
        root->file_path = file_path;
        
        std::istringstream stream(content);
        std::string line;
        int line_number = 1;
        
        while (std::getline(stream, line)) {
            if (line.find("(") != std::string::npos && 
                (line.find("int ") == 0 || line.find("void ") == 0 || 
                 line.find("char ") == 0 || line.find("double ") == 0)) {
                // Function definition
                auto func_node = std::make_shared<EnhancedASTNode>(
                    EnhancedASTNode::FUNCTION_DEF, line, line_number, 0);
                func_node->file_path = file_path;
                root->add_child(func_node);
            } else if (line.find("if") == 0 || line.find("if ") != std::string::npos) {
                // If statement
                auto if_node = std::make_shared<EnhancedASTNode>(
                    EnhancedASTNode::IF_STATEMENT, line, line_number, 0);
                if_node->file_path = file_path;
                root->add_child(if_node);
            } else if (line.find("=") != std::string::npos && line.find("==") == std::string::npos) {
                // Assignment
                auto assign_node = std::make_shared<EnhancedASTNode>(
                    EnhancedASTNode::ASSIGNMENT, line, line_number, 0);
                assign_node->file_path = file_path;
                root->add_child(assign_node);
            } else if (line.find("(") != std::string::npos && line.find(")") != std::string::npos) {
                // Function call
                auto call_node = std::make_shared<EnhancedASTNode>(
                    EnhancedASTNode::FUNCTION_CALL, line, line_number, 0);
                call_node->file_path = file_path;
                root->add_child(call_node);
            }
            
            line_number++;
        }
        
        return root;
    }
    
    void build_symbol_table(const std::shared_ptr<EnhancedASTNode>& ast) {
        // Simplified symbol table building
        traverse_for_symbols(ast);
    }
    
    void traverse_for_symbols(const std::shared_ptr<EnhancedASTNode>& node) {
        if (!node) return;
        
        if (node->type == EnhancedASTNode::FUNCTION_DEF) {
            symbol_table.push_scope();
            // Extract function parameters and add to symbol table
            // Simplified implementation
        }
        
        if (node->type == EnhancedASTNode::VARIABLE_DECL) {
            // Add variable to current scope
            symbol_table.define_symbol(node->value, node->data_type);
        }
        
        for (const auto& child : node->children) {
            traverse_for_symbols(child);
        }
        
        if (node->type == EnhancedASTNode::FUNCTION_DEF) {
            symbol_table.pop_scope();
        }
    }
    
    void build_control_flow_graph(const std::shared_ptr<EnhancedASTNode>& ast) {
        // Simplified CFG building
        traverse_for_cfg(ast, nullptr);
    }
    
    void traverse_for_cfg(const std::shared_ptr<EnhancedASTNode>& node,
                         std::shared_ptr<EnhancedASTNode> predecessor) {
        if (!node) return;
        
        cfg.add_node(node);
        
        if (predecessor) {
            cfg.add_edge(predecessor, node);
        }
        
        for (const auto& child : node->children) {
            traverse_for_cfg(child, node);
        }
    }
    
    std::vector<EnhancedFinding> run_taint_analysis(
        const std::shared_ptr<EnhancedASTNode>& ast,
        const std::string& file_path,
        AdvancedTaintAnalyzer& analyzer) {
        
        std::vector<EnhancedFinding> findings;
        
        // Run taint analysis on each function
        traverse_functions_for_taint(ast, findings, file_path, analyzer);
        
        return findings;
    }
    
    void traverse_functions_for_taint(const std::shared_ptr<EnhancedASTNode>& node,
                                     std::vector<EnhancedFinding>& findings,
                                     const std::string& file_path,
                                     AdvancedTaintAnalyzer& analyzer) {
        if (!node) return;
        
        if (node->type == EnhancedASTNode::FUNCTION_DEF) {
            auto func_findings = analyzer.analyze_function(node, file_path);
            findings.insert(findings.end(), func_findings.begin(), func_findings.end());
        }
        
        for (const auto& child : node->children) {
            traverse_functions_for_taint(child, findings, file_path, analyzer);
        }
    }
};

// Utility functions for output formatting and reporting
class ReportGenerator {
public:
    static void print_findings(const std::vector<EnhancedFinding>& findings) {
        if (findings.empty()) {
            std::cout << "\n✅ No security vulnerabilities found!\n" << std::endl;
            return;
        }

        std::cout << "\n🔍 Security Scan Results\n";
        std::cout << "========================\n\n";

        // Group findings by severity
        std::unordered_map<Severity, std::vector<EnhancedFinding>> grouped_findings;
        for (const auto& finding : findings) {
            grouped_findings[finding.severity].push_back(finding);
        }

        // Print summary
        print_summary(grouped_findings);

        // Print detailed findings
        print_detailed_findings(grouped_findings);
    }

    static void generate_json_report(const std::vector<EnhancedFinding>& findings,
                                   const std::string& output_file) {
#ifdef JSON_SUPPORT_DISABLED
        // Fallback to simple JSON-like format
        std::ofstream output(output_file);
        if (!output.is_open()) {
            std::cerr << "❌ Error: Could not write to " << output_file << std::endl;
            return;
        }

        output << "{\n";
        output << "  \"scan_timestamp\": \"" << get_current_timestamp() << "\",\n";
        output << "  \"total_findings\": " << findings.size() << ",\n";
        output << "  \"findings\": [\n";

        for (size_t i = 0; i < findings.size(); ++i) {
            const auto& finding = findings[i];
            output << "    {\n";
            output << "      \"type\": \"" << vuln_type_to_string(finding.type) << "\",\n";
            output << "      \"severity\": \"" << severity_to_string(finding.severity) << "\",\n";
            output << "      \"title\": \"" << escape_json_string(finding.title) << "\",\n";
            output << "      \"description\": \"" << escape_json_string(finding.description) << "\",\n";
            output << "      \"recommendation\": \"" << escape_json_string(finding.recommendation) << "\",\n";
            output << "      \"file_path\": \"" << escape_json_string(finding.file_path) << "\",\n";
            output << "      \"line_number\": " << finding.line_number << ",\n";
            output << "      \"column_number\": " << finding.column_number << ",\n";
            output << "      \"function_name\": \"" << escape_json_string(finding.function_name) << "\",\n";
            output << "      \"code_snippet\": \"" << escape_json_string(finding.code_snippet) << "\",\n";
            output << "      \"confidence_score\": " << finding.confidence_score << ",\n";
            output << "      \"evidence\": [";

            for (size_t j = 0; j < finding.evidence.size(); ++j) {
                output << "\"" << escape_json_string(finding.evidence[j]) << "\"";
                if (j < finding.evidence.size() - 1) output << ", ";
            }

            output << "]\n";
            output << "    }";
            if (i < findings.size() - 1) output << ",";
            output << "\n";
        }

        output << "  ]\n";
        output << "}\n";
        output.close();
        std::cout << "📄 JSON report saved to: " << output_file << std::endl;

#else
        Json::Value root;
        Json::Value findings_array(Json::arrayValue);

        for (const auto& finding : findings) {
            Json::Value finding_json;
            finding_json["type"] = vuln_type_to_string(finding.type);
            finding_json["severity"] = severity_to_string(finding.severity);
            finding_json["title"] = finding.title;
            finding_json["description"] = finding.description;
            finding_json["recommendation"] = finding.recommendation;
            finding_json["file_path"] = finding.file_path;
            finding_json["line_number"] = finding.line_number;
            finding_json["column_number"] = finding.column_number;
            finding_json["function_name"] = finding.function_name;
            finding_json["code_snippet"] = finding.code_snippet;
            finding_json["confidence_score"] = finding.confidence_score;

            Json::Value evidence_array(Json::arrayValue);
            for (const auto& evidence : finding.evidence) {
                evidence_array.append(evidence);
            }
            finding_json["evidence"] = evidence_array;

            findings_array.append(finding_json);
        }

        root["findings"] = findings_array;
        root["scan_timestamp"] = get_current_timestamp();
        root["total_findings"] = static_cast<int>(findings.size());

        std::ofstream output(output_file);
        if (output.is_open()) {
            output << root;
            output.close();
            std::cout << "📄 JSON report saved to: " << output_file << std::endl;
        } else {
            std::cerr << "❌ Error: Could not write to " << output_file << std::endl;
        }
#endif
    }

private:
    static std::string escape_json_string(const std::string& input) {
        std::string result;
        for (char c : input) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\b': result += "\\b"; break;
                case '\f': result += "\\f"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default:
                    if (c >= 0 && c < 32) {
                        result += "\\u" + std::to_string(static_cast<unsigned char>(c));
                    } else {
                        result += c;
                    }
                    break;
            }
        }
        return result;
    }

    static void print_summary(const std::unordered_map<Severity, std::vector<EnhancedFinding>>& grouped_findings) {
        std::cout << "📊 Summary:\n";

        int critical = grouped_findings.count(Severity::CRITICAL) ?
                      grouped_findings.at(Severity::CRITICAL).size() : 0;
        int high = grouped_findings.count(Severity::HIGH) ?
                  grouped_findings.at(Severity::HIGH).size() : 0;
        int medium = grouped_findings.count(Severity::MEDIUM) ?
                    grouped_findings.at(Severity::MEDIUM).size() : 0;
        int low = grouped_findings.count(Severity::LOW) ?
                 grouped_findings.at(Severity::LOW).size() : 0;

        std::cout << "  🔴 Critical: " << critical << "\n";
        std::cout << "  🟠 High:     " << high << "\n";
        std::cout << "  🟡 Medium:   " << medium << "\n";
        std::cout << "  🟢 Low:      " << low << "\n\n";
    }

    static void print_detailed_findings(const std::unordered_map<Severity, std::vector<EnhancedFinding>>& grouped_findings) {
        // Print in order of severity
        std::vector<Severity> severity_order = {Severity::CRITICAL, Severity::HIGH, Severity::MEDIUM, Severity::LOW};

        for (Severity sev : severity_order) {
            if (grouped_findings.count(sev) == 0) continue;

            std::cout << "📋 " << severity_to_string(sev) << " Severity Issues:\n";
            std::cout << std::string(40, '-') << "\n";

            for (const auto& finding : grouped_findings.at(sev)) {
                print_single_finding(finding);
            }
            std::cout << "\n";
        }
    }

    static void print_single_finding(const EnhancedFinding& finding) {
        std::cout << "🚨 " << finding.title << "\n";
        std::cout << "   Type: " << vuln_type_to_string(finding.type) << "\n";
        std::cout << "   File: " << finding.file_path << ":" << finding.line_number;
        if (finding.column_number > 0) {
            std::cout << ":" << finding.column_number;
        }
        std::cout << "\n";

        if (!finding.function_name.empty()) {
            std::cout << "   Function: " << finding.function_name << "\n";
        }

        std::cout << "   Description: " << finding.description << "\n";

        if (!finding.code_snippet.empty()) {
            std::cout << "   Code: " << finding.code_snippet << "\n";
        }

        if (!finding.recommendation.empty()) {
            std::cout << "   💡 Recommendation: " << finding.recommendation << "\n";
        }

        std::cout << "   🎯 Confidence: " << std::fixed << std::setprecision(1)
                  << (finding.confidence_score * 100) << "%\n";

        if (!finding.evidence.empty()) {
            std::cout << "   📝 Evidence:\n";
            for (const auto& evidence : finding.evidence) {
                std::cout << "      • " << evidence << "\n";
            }
        }

        std::cout << "\n";
    }

    static std::string severity_to_string(Severity sev) {
        switch (sev) {
            case Severity::LOW: return "LOW";
            case Severity::MEDIUM: return "MEDIUM";
            case Severity::HIGH: return "HIGH";
            case Severity::CRITICAL: return "CRITICAL";
            default: return "UNKNOWN";
        }
    }

    static std::string vuln_type_to_string(VulnType type) {
        switch (type) {
            case VulnType::AUTH_BYPASS: return "AUTH_BYPASS";
            case VulnType::AUTHORIZATION_FLAW: return "AUTHORIZATION_FLAW";
            case VulnType::BUSINESS_RULE_VIOLATION: return "BUSINESS_RULE_VIOLATION";
            case VulnType::DATA_FLOW_ANOMALY: return "DATA_FLOW_ANOMALY";
            case VulnType::TOCTOU_RACE_CONDITION: return "TOCTOU_RACE_CONDITION";
            case VulnType::HARDCODED_CREDENTIAL: return "HARDCODED_CREDENTIAL";
            case VulnType::MISSING_VALIDATION: return "MISSING_VALIDATION";
            case VulnType::BUFFER_OVERFLOW: return "BUFFER_OVERFLOW";
            case VulnType::SQL_INJECTION: return "SQL_INJECTION";
            case VulnType::XSS: return "XSS";
            case VulnType::COMMAND_INJECTION: return "COMMAND_INJECTION";
            case VulnType::PATH_TRAVERSAL: return "PATH_TRAVERSAL";
            case VulnType::INSECURE_CRYPTO: return "INSECURE_CRYPTO";
            case VulnType::WEAK_RANDOM: return "WEAK_RANDOM";
            case VulnType::INFORMATION_DISCLOSURE: return "INFORMATION_DISCLOSURE";
            default: return "UNKNOWN";
        }
    }

    static std::string get_current_timestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
};

// Command line argument parser
class ArgumentParser {
private:
    std::vector<std::string> files_to_scan;
    std::string config_file;
    std::string output_file;
    bool json_output = false;
    bool verbose = false;
    bool help_requested = false;

public:
    bool parse(int argc, char* argv[]) {
        for (int i = 1; i < argc; i++) {
            std::string arg = argv[i];

            if (arg == "-h" || arg == "--help") {
                help_requested = true;
                return true;
            } else if (arg == "-c" || arg == "--config") {
                if (i + 1 < argc) {
                    config_file = argv[++i];
                } else {
                    std::cerr << "Error: --config requires a file path" << std::endl;
                    return false;
                }
            } else if (arg == "-o" || arg == "--output") {
                if (i + 1 < argc) {
                    output_file = argv[++i];
                } else {
                    std::cerr << "Error: --output requires a file path" << std::endl;
                    return false;
                }
            } else if (arg == "-j" || arg == "--json") {
                json_output = true;
            } else if (arg == "-v" || arg == "--verbose") {
                verbose = true;
            } else if (arg.length() > 0 && arg[0] == '-') {
                std::cerr << "Error: Unknown option " << arg << std::endl;
                return false;
            } else {
                files_to_scan.push_back(arg);
            }
        }

        if (!help_requested && files_to_scan.empty()) {
            std::cerr << "Error: No files specified for scanning" << std::endl;
            return false;
        }

        return true;
    }

    void print_help() const {
        std::cout << "Advanced Business Logic Security Scanner\n";
        std::cout << "========================================\n\n";
        std::cout << "Usage: scanner [OPTIONS] <file1> [file2] ...\n\n";
        std::cout << "Options:\n";
        std::cout << "  -h, --help           Show this help message\n";
        std::cout << "  -c, --config FILE    Use custom configuration file\n";
        std::cout << "  -o, --output FILE    Save results to file\n";
        std::cout << "  -j, --json           Output results in JSON format\n";
        std::cout << "  -v, --verbose        Enable verbose output\n\n";
        std::cout << "Examples:\n";
        std::cout << "  scanner main.cpp\n";
        std::cout << "  scanner -c rules.json -o report.json -j *.cpp\n";
        std::cout << "  scanner --verbose --output results.txt src/*.c\n\n";
    }

    const std::vector<std::string>& get_files() const { return files_to_scan; }
    const std::string& get_config_file() const { return config_file; }
    const std::string& get_output_file() const { return output_file; }
    bool is_json_output() const { return json_output; }
    bool is_verbose() const { return verbose; }
    bool is_help_requested() const { return help_requested; }
};

// Main function
int main(int argc, char* argv[]) {
    std::cout << "🔒 Advanced Business Logic Security Scanner v2.0\n";
    std::cout << "================================================\n\n";

    ArgumentParser parser;
    if (!parser.parse(argc, argv)) {
        parser.print_help();
        return 1;
    }

    if (parser.is_help_requested()) {
        parser.print_help();
        return 0;
    }

    // Initialize scanner with optional config file
    AdvancedBusinessLogicScanner scanner(parser.get_config_file());

    std::vector<EnhancedFinding> all_findings;

    // Scan each file
    for (const auto& file_path : parser.get_files()) {
        if (parser.is_verbose()) {
            std::cout << "🔍 Scanning: " << file_path << std::endl;
        }

        auto findings = scanner.scan_file(file_path);
        all_findings.insert(all_findings.end(), findings.begin(), findings.end());

        if (parser.is_verbose()) {
            std::cout << "   Found " << findings.size() << " potential issues\n";
        }
    }

    // Generate output
    if (parser.is_json_output()) {
        std::string output_file = parser.get_output_file();
        if (output_file.empty()) {
            output_file = "security_report.json";
        }
        ReportGenerator::generate_json_report(all_findings, output_file);
    } else {
        ReportGenerator::print_findings(all_findings);

        // Save to text file if requested
        if (!parser.get_output_file().empty()) {
            std::ofstream output(parser.get_output_file());
            if (output.is_open()) {
                // Redirect cout to file temporarily
                std::streambuf* orig = std::cout.rdbuf();
                std::cout.rdbuf(output.rdbuf());

                ReportGenerator::print_findings(all_findings);

                // Restore cout
                std::cout.rdbuf(orig);
                output.close();

                std::cout << "📄 Report saved to: " << parser.get_output_file() << std::endl;
            } else {
                std::cerr << "❌ Error: Could not write to " << parser.get_output_file() << std::endl;
            }
        }
    }

    // Print final summary
    std::cout << "\n🎯 Scan completed. Total findings: " << all_findings.size() << std::endl;

    // Return appropriate exit code
    bool has_critical_or_high = std::any_of(all_findings.begin(), all_findings.end(),
        [](const EnhancedFinding& f) {
            return f.severity == Severity::CRITICAL || f.severity == Severity::HIGH;
        });

    return has_critical_or_high ? 1 : 0;
}