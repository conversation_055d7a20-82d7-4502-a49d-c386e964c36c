{"scan_timestamp": "2025-06-08 16:18:28", "total_findings": 7, "findings": [{"type": "MISSING_VALIDATION", "severity": "HIGH", "title": "unsafe_buffer_operations", "description": "Unsafe buffer operations detected", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 33, "column_number": 0, "function_name": "", "code_snippet": "    strcpy(buffer, input);", "confidence_score": 0.8, "evidence": ["Pattern matched: strcpy", "Missing required validation context"]}, {"type": "MISSING_VALIDATION", "severity": "HIGH", "title": "unsafe_buffer_operations", "description": "Unsafe buffer operations detected", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 41, "column_number": 0, "function_name": "", "code_snippet": "    sprintf(command, \"ls %s\", user_input);", "confidence_score": 0.8, "evidence": ["Pattern matched: sprintf", "Missing required validation context"]}, {"type": "MISSING_VALIDATION", "severity": "HIGH", "title": "unsafe_buffer_operations", "description": "Unsafe buffer operations detected", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 51, "column_number": 0, "function_name": "", "code_snippet": "    gets(user_data);  // Unsafe function", "confidence_score": 0.8, "evidence": ["Pattern matched: gets", "Missing required validation context"]}, {"type": "MISSING_VALIDATION", "severity": "HIGH", "title": "unsafe_buffer_operations", "description": "Unsafe buffer operations detected", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 54, "column_number": 0, "function_name": "", "code_snippet": "    strcpy(processed_data, user_data);", "confidence_score": 0.8, "evidence": ["Pattern matched: strcpy", "Missing required validation context"]}, {"type": "BUSINESS_RULE_VIOLATION", "severity": "HIGH", "title": "missing_business_validation", "description": "Missing validation in business logic", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 21, "column_number": 0, "function_name": "", "code_snippet": "void process_payment(double amount) {", "confidence_score": 0.7, "evidence": ["Pattern matched: amount", "Missing required validation context"]}, {"type": "BUSINESS_RULE_VIOLATION", "severity": "HIGH", "title": "missing_business_validation", "description": "Missing validation in business logic", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 23, "column_number": 0, "function_name": "", "code_snippet": "    printf(\"Processing payment of $%.2f\\n\", amount);", "confidence_score": 0.7, "evidence": ["Pattern matched: payment", "Missing required validation context"]}, {"type": "BUSINESS_RULE_VIOLATION", "severity": "HIGH", "title": "missing_business_validation", "description": "Missing validation in business logic", "recommendation": "Add proper validation checks before this operation", "file_path": "test_vulnerable.c", "line_number": 75, "column_number": 0, "function_name": "", "code_snippet": "        printf(\"Enter payment amount: \");", "confidence_score": 0.7, "evidence": ["Pattern matched: payment", "Missing required validation context"]}]}