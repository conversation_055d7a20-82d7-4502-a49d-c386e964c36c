#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Test file with various security vulnerabilities for the scanner to detect

int authenticate_user(char* username, char* password) {
    // Hardcoded authentication bypass - should be detected
    if (strcmp(password, "admin") == 0) {
        return 1;
    }
    
    // Another hardcoded credential
    if (strcmp(username, "root") == 0 && strcmp(password, "1234") == 0) {
        return 1;
    }
    
    return 0;
}

void process_payment(double amount) {
    // Missing validation for business logic - should be detected
    printf("Processing payment of $%.2f\n", amount);
    
    // This should trigger missing validation warning
    // No check for amount > 0 or reasonable limits
}

void unsafe_copy(char* input) {
    char buffer[100];
    
    // Unsafe buffer operation - should be detected
    strcpy(buffer, input);
    printf("Buffer contains: %s\n", buffer);
}

void command_execution(char* user_input) {
    char command[256];
    
    // Potential command injection - should be detected
    sprintf(command, "ls %s", user_input);
    system(command);
}

void taint_example() {
    char user_data[256];
    char processed_data[256];
    
    // Taint source - user input
    printf("Enter data: ");
    gets(user_data);  // Unsafe function
    
    // Taint propagation
    strcpy(processed_data, user_data);
    
    // Taint sink - dangerous function with tainted data
    system(processed_data);
}

int main() {
    char username[50];
    char password[50];
    double payment_amount;
    char file_path[100];
    
    printf("Enter username: ");
    scanf("%s", username);
    
    printf("Enter password: ");
    scanf("%s", password);
    
    if (authenticate_user(username, password)) {
        printf("Authentication successful!\n");
        
        printf("Enter payment amount: ");
        scanf("%lf", &payment_amount);
        process_payment(payment_amount);
        
        printf("Enter file path: ");
        scanf("%s", file_path);
        unsafe_copy(file_path);
        
        command_execution(file_path);
    } else {
        printf("Authentication failed!\n");
    }
    
    taint_example();
    
    return 0;
}
