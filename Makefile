# Makefile for Advanced Business Logic Security Scanner

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
TARGET = scanner
SOURCE = scanner.cpp

# Check if jsoncpp is available
JSONCPP_AVAILABLE := $(shell pkg-config --exists jsoncpp && echo "yes" || echo "no")

ifeq ($(JSONCPP_AVAILABLE),yes)
    CXXFLAGS += $(shell pkg-config --cflags jsoncpp) -DJSON_SUPPORT_ENABLED
    LDFLAGS += $(shell pkg-config --libs jsoncpp)
    $(info Building with JSON support)
else
    $(info Building without JSON support - using fallback implementation)
endif

.PHONY: all clean test install

all: $(TARGET)

$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE) $(LDFLAGS)

clean:
	rm -f $(TARGET) *.o security_report.* test_report.*

test: $(TARGET) test_vulnerable.c
	@echo "Running security scan on test file..."
	./$(TARGET) --verbose test_vulnerable.c
	@echo ""
	@echo "Running JSON output test..."
	./$(TARGET) --json --output test_report.json test_vulnerable.c

install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

help:
	@echo "Available targets:"
	@echo "  all     - Build the scanner"
	@echo "  clean   - Remove built files"
	@echo "  test    - Run tests on sample vulnerable code"
	@echo "  install - Install to /usr/local/bin"
	@echo "  help    - Show this help message"
