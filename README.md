# Advanced Business Logic Security Scanner

A comprehensive C++ security scanner designed to detect business logic vulnerabilities, authentication bypasses, and common security flaws in C/C++ source code.

## Features

### 🔍 Advanced Analysis Capabilities
- **Business Logic Rule Engine**: Configurable rules for detecting business logic flaws
- **Taint Analysis**: Inter-procedural data flow analysis to track user input
- **Pattern Matching**: Regex-based and custom validation patterns
- **Control Flow Analysis**: Basic control flow graph construction
- **Symbol Table Management**: Scope-aware variable tracking

### 🚨 Vulnerability Detection
- **Authentication Bypass**: Hardcoded credentials and bypass logic
- **Authorization Flaws**: Missing access controls
- **Business Rule Violations**: Missing validation in financial/business operations
- **Buffer Overflows**: Unsafe string operations (strcpy, sprintf, gets)
- **Data Flow Anomalies**: Tainted data reaching sensitive functions
- **Missing Validation**: Insufficient input validation

### 📊 Reporting Features
- **Console Output**: Colored, formatted security findings
- **JSON Reports**: Machine-readable output for CI/CD integration
- **Severity Classification**: Critical, High, Medium, Low severity levels
- **Confidence Scoring**: Confidence levels for each finding
- **Evidence Collection**: Detailed evidence and recommendations

## Building

### Prerequisites
- C++17 compatible compiler (GCC 7+ or Clang 5+)
- Make
- Optional: jsoncpp library for enhanced JSON support

### Compilation
```bash
# Basic build
make

# Clean build artifacts
make clean

# Run tests
make test

# Install to system
sudo make install
```

## Usage

### Basic Scanning
```bash
# Scan a single file
./scanner main.cpp

# Scan multiple files
./scanner src/*.cpp include/*.h

# Verbose output
./scanner --verbose main.cpp
```

### Output Options
```bash
# Generate JSON report
./scanner --json --output report.json main.cpp

# Save text report to file
./scanner --output report.txt main.cpp

# Both console and file output
./scanner --verbose --output detailed_report.txt src/*.cpp
```

### Configuration
```bash
# Use custom rule configuration (requires JSON support)
./scanner --config custom_rules.json main.cpp
```

## Command Line Options

| Option | Description |
|--------|-------------|
| `-h, --help` | Show help message |
| `-c, --config FILE` | Use custom configuration file |
| `-o, --output FILE` | Save results to file |
| `-j, --json` | Output results in JSON format |
| `-v, --verbose` | Enable verbose output |

## Example Output

### Console Output
```
🔒 Advanced Business Logic Security Scanner v2.0
================================================

🔍 Security Scan Results
========================

📊 Summary:
  🔴 Critical: 0
  🟠 High:     3
  🟡 Medium:   1
  🟢 Low:      0

📋 HIGH Severity Issues:
----------------------------------------
🚨 unsafe_buffer_operations
   Type: MISSING_VALIDATION
   File: main.cpp:45
   Description: Unsafe buffer operations detected
   Code: strcpy(buffer, user_input);
   💡 Recommendation: Add proper validation checks before this operation
   🎯 Confidence: 80.0%
   📝 Evidence:
      • Pattern matched: strcpy
      • Missing required validation context
```

### JSON Output
```json
{
  "scan_timestamp": "2024-01-15 10:30:45",
  "total_findings": 3,
  "findings": [
    {
      "type": "MISSING_VALIDATION",
      "severity": "HIGH",
      "title": "unsafe_buffer_operations",
      "description": "Unsafe buffer operations detected",
      "file_path": "main.cpp",
      "line_number": 45,
      "confidence_score": 0.8,
      "evidence": ["Pattern matched: strcpy"]
    }
  ]
}
```

## Detected Vulnerability Types

### Business Logic Flaws
- Hardcoded authentication bypasses
- Missing payment/amount validation
- Insufficient business rule enforcement

### Security Vulnerabilities
- Buffer overflow vulnerabilities
- Command injection risks
- Tainted data flow to sensitive functions
- Missing input validation

## Architecture

The scanner consists of several key components:

1. **Enhanced AST Node**: Represents code structure with metadata
2. **Symbol Table**: Tracks variable scope and types
3. **Control Flow Graph**: Models program execution paths
4. **Pattern Matcher**: Flexible pattern matching system
5. **Taint Analyzer**: Tracks data flow from sources to sinks
6. **Business Logic Rule Engine**: Configurable vulnerability detection
7. **Report Generator**: Formats and outputs findings

## Extending the Scanner

### Adding Custom Rules
The scanner supports custom rules through configuration files (when JSON support is enabled):

```json
{
  "rules": [
    {
      "name": "custom_rule",
      "description": "Custom vulnerability pattern",
      "vuln_type": "MISSING_VALIDATION",
      "severity": "HIGH",
      "patterns": ["dangerous_function\\("],
      "required_context": ["validation", "check"],
      "confidence": 0.8
    }
  ]
}
```

## Limitations

- Simplified parser (for production use, integrate with Clang LibTooling)
- Basic inter-procedural analysis
- Limited context-sensitive analysis
- No support for complex data structures

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
